# Human Behaviour Analysis

A video action recognition project using the UCF101 dataset to classify human actions in videos.

## Overview

This project implements a deep learning approach for recognizing human actions in videos using the UCF101 dataset. The system extracts frames from videos, uses a pre-trained VGG16 model for feature extraction, and trains a neural network to classify actions across 101 different categories.

## Dataset

The project uses the [UCF101 dataset](https://www.crcv.ucf.edu/data/UCF101.php), which contains:
- 13,320 videos from 101 action categories
- Actions include sports, musical instruments, human-object interaction, and body-motion activities
- Standard train/test splits provided by the dataset

## Architecture

The model follows a two-stage approach:
1. **Feature Extraction**: Uses pre-trained VGG16 (ImageNet weights) to extract features from video frames
2. **Classification**: A fully connected neural network with dropout layers for action classification

### Model Details
- Input: 224x224x3 RGB frames
- Feature extractor: VGG16 (without top layers)
- Classifier: 5-layer fully connected network (1024→512→256→128→101 neurons)
- Output: 101 action classes

## Project Structure

```
├── VideoPreprocessing.py    # Video frame extraction and data preparation
├── Model.py                 # Neural network architecture and training functions
├── Train.py                 # Training pipeline
├── Evaluate.py              # Model evaluation and prediction
├── UCF/                     # Dataset directory
│   ├── ucfTrainTestlist/    # Train/test split files
│   ├── train_1/             # Extracted frames
│   └── ckpt/                # Model checkpoints
└── README.md
```

## Usage

### Training
1. Place UCF101 videos in the appropriate directory
2. Run the training pipeline:
```python
python Train.py
```

### Evaluation
Evaluate the trained model:
```python
python Evaluate.py
```

## Requirements

- Python 3.x
- TensorFlow/Keras
- OpenCV
- NumPy
- Pandas
- scikit-learn
- tqdm

## Notes

- The project extracts one frame per second from each video for training
- Features are normalized after VGG16 extraction
- Model checkpoints are saved during training
- Supports both single video prediction and batch evaluation

This is a straightforward implementation for educational/research purposes using standard computer vision techniques for action recognition.
